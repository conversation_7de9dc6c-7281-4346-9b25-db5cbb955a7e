import os

import requests
from bs4 import BeautifulSoup
import re

def main():
    # Replace these with your actual login credentials
    EMAIL = os.environ.get("EMAIL")
    PASSWORD = os.environ.get("PLAYTIME_SCHEDULER_PASSWORD")

    # Start a session to persist cookies
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0 Safari/537.36"
    })

    # 1. Load login page (to get initial cookies or tokens if any)
    login_url = "https://playtimescheduler.com/login.php"
    r = session.get(login_url)
    soup = BeautifulSoup(r.text, "html.parser")

    # 2. Prepare login payload
    # Inspect login.php to confirm form field names (usually "email" and "password")
    payload = {
        "email": EMAIL,
        "password": PASSWORD,
        "submit": "Log in"
    }

    # 3. Post login credentials
    resp = session.post("https://playtimescheduler.com/login.php", data=payload)

    if "logout" not in resp.text.lower():
        print("<PERSON><PERSON> failed. Check your credentials or form fields.")
    else:
        print("Login successful!")

    # Step 4: Load the schedule page
    calendar_url = "https://playtimescheduler.com/index.php"
    import time
    time.sleep(1)
    resp = session.get(calendar_url)
    soup = BeautifulSoup(resp.text, "html.parser")

    # Step 3: Extract all session buttons
    session_data = []
    import ipdb;
    ipdb.set_trace()

    pattern = re.compile(
        r"(?P<levels>\d+\.\d+-\d+\.\d+)"
        r"(?P<time>\d{1,2}:\d{2}[AP]?)"
        r"(?P<count>\d+)$"
    )

    for calendar_div in soup.select("div.calendarSessions"):
        session_date = calendar_div.get("data-date")  # e.g., "08/04/2025"

        for btn in calendar_div.select("button.session-button"):
            session_id = btn.get("data-session-id")
            location = btn.get("title")
            text = btn.text.strip()

            match = pattern.match(text)
            levels = ""
            time = ""
            count = ""
            if match:
                levels = match.group("levels")
                time = match.group("time")
                count = match.group("count")

            session_data.append({
                "date": session_date,
                "session_id": session_id,
                "location": location,
                "time": time if time else None,
                "levels": levels if levels else None,
                "count": count if count else None
            })

    ipdb.set_trace()
    # Output the data
    from pprint import pprint
    pprint(session_data)

if __name__ == "__main__":
    main()