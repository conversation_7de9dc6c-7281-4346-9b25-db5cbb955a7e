[tool.poetry]
name = "Pickleball Web Scraper"
version = "0.1.0"
description = "stuff"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"

[tool.poetry.dependencies]
python = "^3.13"
pandas = "2.3.1"
psycopg = { version = "~3.2", extras = ["binary"] }
opentelemetry-sdk = "1.24.0"
opentelemetry-exporter-otlp = "1.24.0"
doppler-env = "*"
redis = "*"
ipdb = "^0.13.13"
boto3 = "*"
jinja2 = "*"
pyyaml = "^6.0.2"
bs4 = "*"


[tool.poetry.group.dev.dependencies]
black = "~24.4"
isort = "~5.13"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 150
exclude = ".*.sql|.*.md|.*.pyc|.*.yaml|.*.yml|.*.json|do-not-commit|venv"

# https://pycqa.github.io/isort/docs/configuration/options.html
[tool.isort]
profile = "black"
float_to_top = true
skip = [
    ".gitignore",
    ".dockerignore",
    ".venv"
]
skip_glob = [
    "**/.venv/**"
]
extend_skip = [".venv"]

# https://mypy.readthedocs.io/en/stable/config_file.html#confval-exclude
[tool.mypy]
python_version = "^3.12"
warn_return_any = true
warn_unused_configs = true
check_untyped_defs = true
disallow_untyped_defs = true
files = []
exclude = [
'venv',
'.venv',
]
disable_error_code = ["index", "import", "assignment",]

[tool.vulture]
min_confidence = 80
sort_by_size = true
exclude = []

[tool.pydocstyle]
convention = "numpy"
add-ignore = "D100,D104"
match = "^(?!.*stream).*.py$"
