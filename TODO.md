# Stuff that needs done

so much...

1. Self-service for masochist, I mean devs, to pull valid node configs from DO
2. Use a database to store node usage
3. Don't upload everything to DO...
4. Update the user experience for configuration from e2e
5. Enable other object storage providers
6. Update the health check to do things
   1. Call it manually from local to update as needed
7. Update the provision directory to provision to other machine types