# Nebula

https://github.com/slackhq/nebula
https://nebula.defined.net/docs/guides/quick-start/#running-nebula

It's a VPN... like tailscale-lite, that doesn't fuck with your DNS.

> NOTE: This config manager only supports a single nebula network.

# Getting Started

These instructions assume that you have access to a DO bucket to get and put certs.

## Pre-reqs

1. Install pyenv (or just use w/e python version you have):
   1. https://github.com/pyenv-win/pyenv-win/blob/master/docs/installation.md#powershell
   2. WINDOWS: https://github.com/pyenv-win/pyenv-win/blob/master/docs/installation.md#add-system-settings
2. Check your pyenv version:
   1. pyenv version
3. Update pyenv packages
   1. ``pyenv update``
4. Install python 3.13
```bash
pyenv install 3.13.5
pyenv local 3.13.5
```
6. Install dependencies
```bash
poetry lock
poetry install
```
7. Install doppler and use the cackalacky-infra project (dev)
```bash
doppler setup
# select "cackalacky-infra" as the project
# select dev as the config (dev is prod)
```

### nebula client:

On windows, I install mine to C:\nebula-vpn
Then update my environment variables to include the path to the nebula executable.

Install yours however you want, just make sure that you can run in terminal / powershell:

```bash
nebula -version
nebula-cert -version
```

## .env

1. Copy .env.example to .env

### Config Storage - Digital Ocean

1. Update .env with your DO credentials
2. Update .env with your bucket name
3. Update .env with your region

### Nebula Specific Config Values

Change these as you see fit:

```bash
CA_NAME=...
LIGHTHOUSE_NODE=...
LIGHTHOUSE_IP=<the nebula ip to be assigned to the lighthouse>
IP_RANGE=16
ROUTABLE_IP=<the external/public ip of the lighthouse>
```

However, once you create these, you should not change them (unless you expand this to support multiple networks).



# Configuration Generation

Run this first. Over provision certs - they're free.

## Usage

```bash
# run in this order
doppler run -- poetry run python main.py create-ca
doppler run -- poetry run python main.py create-config --type lighthouse
doppler run -- poetry run python main.py create-config --type host --network local
doppler run -- poetry run python main.py create-certs --type lighthouse
doppler run -- poetry run python main.py create-certs --type host --network local
doppler run -- poetry run python main.py upload-to-do
```

## CA Certs

You must create these first... else nothing will work.

1. Create CA Certs
   1. Run generation/create-certificates.sh
3. Run create-certificates.sh
4. Run create-configs.sh
5. Run install-nebula-service.sh

## Nodes - lighthouse

Create this first if you don't have one.

1. Run provision/lighthouse.sh

## Nodes - host(s)

1. Run provision/ship.sh

# Note

## VM VirtualBox

~~I cannot get punchy to work... on physical machines or cloud it works fine.~~

Update got it working via:
 - bridge mode in virtualbox
   - ``config.vm.network "public_network", bridge: "Intel(R) Ethernet Connection (7) I219-V"``
 - Spin up the VM
 - Find the ehternet interface on the VM
   - ``ip a``
 - Get that IP
 - Update the VM config file
   - listeners => 0.0.0.0 to the IP from above
 - Adjust the VM firewall
    - ``sudo ufw allow in on enp0s8 to any port 4242 proto udp``
 - Had to port forward from router
   - Unifi made this easy
   - Just use the IP from ``ip a`` and your specific interface
 - Restart nebula

# Health Check
